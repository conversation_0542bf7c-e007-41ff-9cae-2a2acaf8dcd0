#!/usr/bin/env python3
"""
Debug script để kiểm tra DOCX upload
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

async def test_docx_upload_detailed():
    """Test DOCX upload với debug chi tiết"""
    print("🔍 Debugging DOCX Upload")
    print("=" * 50)
    
    try:
        from app.services.supabase_storage_service import get_supabase_storage_service
        
        service = get_supabase_storage_service()
        
        # Check service availability
        print("1. Checking Supabase service...")
        if not service.is_available():
            print("❌ Supabase service not available")
            return
        print("✅ Supabase service is available")
        
        # Create a proper DOCX file content
        print("\n2. Creating test DOCX content...")
        
        # Minimal valid DOCX content (ZIP format with required files)
        import zipfile
        import io
        
        # Create a minimal DOCX file in memory
        docx_buffer = io.BytesIO()
        
        with zipfile.ZipFile(docx_buffer, 'w', zipfile.ZIP_DEFLATED) as docx_zip:
            # Add [Content_Types].xml
            content_types = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
    <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
    <Default Extension="xml" ContentType="application/xml"/>
    <Override PartName="/word/document.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml"/>
</Types>'''
            docx_zip.writestr('[Content_Types].xml', content_types)
            
            # Add _rels/.rels
            rels = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="word/document.xml"/>
</Relationships>'''
            docx_zip.writestr('_rels/.rels', rels)
            
            # Add word/document.xml
            document = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
    <w:body>
        <w:p>
            <w:r>
                <w:t>Test DOCX Content for Upload</w:t>
            </w:r>
        </w:p>
    </w:body>
</w:document>'''
            docx_zip.writestr('word/document.xml', document)
        
        docx_content = docx_buffer.getvalue()
        print(f"✅ Created DOCX content ({len(docx_content)} bytes)")
        
        # Test upload_document_file directly
        print("\n3. Testing upload_document_file with DOCX...")
        result = await service.upload_document_file(
            file_content=docx_content,
            book_id="debug_guide",
            lesson_id="debug_lesson",
            original_filename="debug_test.docx",
            file_type="docx"
        )
        
        print(f"Upload result: {result}")
        
        if result.get("success"):
            print("✅ DOCX upload successful!")
            print(f"   File URL: {result.get('file_url')}")
            print(f"   Upload time: {result.get('uploaded_at')}")
            print(f"   File path: {result.get('file_path')}")
            
            # Test if URL is accessible
            import httpx
            async with httpx.AsyncClient() as client:
                try:
                    response = await client.head(result.get('file_url'))
                    print(f"✅ File URL is accessible (status: {response.status_code})")
                    print(f"   Content-Type: {response.headers.get('content-type', 'N/A')}")
                except Exception as e:
                    print(f"⚠️ Could not test URL accessibility: {e}")
                    
        else:
            print(f"❌ DOCX upload failed: {result.get('error')}")
            
        # Test upload_docx_file method
        print("\n4. Testing upload_docx_file method...")
        result2 = await service.upload_docx_file(
            file_content=docx_content,
            book_id="debug_guide2",
            lesson_id="debug_lesson2",
            original_filename="debug_test2.docx"
        )
        
        if result2.get("success"):
            print("✅ upload_docx_file successful!")
            print(f"   File URL: {result2.get('file_url')}")
        else:
            print(f"❌ upload_docx_file failed: {result2.get('error')}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

async def test_guide_processing():
    """Test xử lý guide với DOCX"""
    print("\n🔍 Testing Guide Processing with DOCX")
    print("=" * 50)
    
    try:
        # Create test DOCX content
        import zipfile
        import io
        
        docx_buffer = io.BytesIO()
        
        with zipfile.ZipFile(docx_buffer, 'w', zipfile.ZIP_DEFLATED) as docx_zip:
            content_types = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
    <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
    <Default Extension="xml" ContentType="application/xml"/>
    <Override PartName="/word/document.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml"/>
</Types>'''
            docx_zip.writestr('[Content_Types].xml', content_types)
            
            rels = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="word/document.xml"/>
</Relationships>'''
            docx_zip.writestr('_rels/.rels', rels)
            
            document = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
    <w:body>
        <w:p>
            <w:r>
                <w:t>This is a test guide document for processing. It contains instructions and guidance for users.</w:t>
            </w:r>
        </w:p>
    </w:body>
</w:document>'''
            docx_zip.writestr('word/document.xml', document)
        
        docx_content = docx_buffer.getvalue()
        
        # Test upload
        print("1. Testing DOCX upload for guide...")
        from app.services.supabase_storage_service import get_supabase_storage_service
        
        supabase_service = get_supabase_storage_service()
        upload_result = await supabase_service.upload_docx_file(
            file_content=docx_content,
            book_id="guide_test",
            lesson_id="guide_lesson",
            original_filename="test_guide.docx"
        )
        
        if upload_result.get("success"):
            print("✅ Guide DOCX uploaded successfully!")
            file_url = upload_result.get("file_url")
            uploaded_at = upload_result.get("uploaded_at")
            
            # Test Qdrant processing
            print("\n2. Testing Qdrant processing for guide...")
            from app.services.qdrant_service import get_qdrant_service
            
            qdrant_service = get_qdrant_service()
            
            # Process as guide
            result = await qdrant_service.process_textbook(
                book_id="guide_test",
                content="This is a test guide document for processing. It contains instructions and guidance for users.",
                lesson_id="guide_lesson",
                content_type="guide",
                file_url=file_url,
                uploaded_at=uploaded_at
            )
            
            if result.get("success"):
                print("✅ Guide processing successful!")
                print(f"   Collection: {result.get('collection_name')}")
                print(f"   Total chunks: {result.get('total_chunks')}")
                
                # Test get guides API
                print("\n3. Testing get guides API...")
                guides_result = await qdrant_service.get_lessons_by_type(content_type="guide")
                
                if guides_result.get("success"):
                    guides = guides_result.get("lessons", [])
                    print(f"✅ Found {len(guides)} guides")
                    
                    # Find our test guide
                    test_guide = None
                    for guide in guides:
                        if guide.get("book_id") == "guide_test":
                            test_guide = guide
                            break
                    
                    if test_guide:
                        print("✅ Test guide found:")
                        print(f"   Book ID: {test_guide.get('book_id')}")
                        print(f"   Lesson ID: {test_guide.get('lesson_id')}")
                        print(f"   File URL: {test_guide.get('file_url')}")
                        print(f"   Content Type: {test_guide.get('content_type')}")
                    else:
                        print("❌ Test guide not found in results")
                else:
                    print(f"❌ Get guides failed: {guides_result.get('error')}")
            else:
                print(f"❌ Guide processing failed: {result.get('error')}")
        else:
            print(f"❌ Guide DOCX upload failed: {upload_result.get('error')}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main debug function"""
    await test_docx_upload_detailed()
    await test_guide_processing()
    
    print("\n✅ Debug completed!")
    print("\n💡 If DOCX upload works here but not in your app:")
    print("   1. Check if the DOCX file is valid")
    print("   2. Check file size limits")
    print("   3. Check content-type headers")
    print("   4. Check Supabase bucket permissions")

if __name__ == "__main__":
    asyncio.run(main())
