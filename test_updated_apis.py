#!/usr/bin/env python3
"""
Test script cho các API đã được cập nhật
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

async def test_supabase_docx_support():
    """Test Supabase service với DOCX"""
    print("🔍 Testing Supabase DOCX Support")
    print("=" * 40)
    
    try:
        from app.services.supabase_storage_service import get_supabase_storage_service
        
        service = get_supabase_storage_service()
        if not service.is_available():
            print("⚠️ Supabase service not available")
            return
        
        # Create test DOCX content (minimal DOCX structure)
        docx_content = b'PK\x03\x04\x14\x00\x00\x00\x08\x00\x00\x00!\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x13\x00\x00\x00[Content_Types].xmlPK\x01\x02\x14\x00\x14\x00\x00\x00\x08\x00\x00\x00!\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x13\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x80\x01\x00\x00\x00\x00[Content_Types].xmlPK\x05\x06\x00\x00\x00\x00\x01\x00\x01\x00A\x00\x00\x00\x00\x00\x00\x00\x00\x00'
        
        print("📤 Testing DOCX upload...")
        result = await service.upload_docx_file(
            file_content=docx_content,
            book_id="test_guide",
            lesson_id="guide_lesson",
            original_filename="test_guide.docx"
        )
        
        if result.get("success"):
            print("✅ DOCX upload successful!")
            print(f"   File URL: {result.get('file_url')}")
            print(f"   Upload time: {result.get('uploaded_at')}")
            return result.get('file_url')
        else:
            print(f"❌ DOCX upload failed: {result.get('error')}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

async def test_lessons_api():
    """Test API get lessons với filter"""
    print("\n🔍 Testing Lessons API")
    print("=" * 40)
    
    try:
        from app.services.qdrant_service import get_qdrant_service
        
        qdrant_service = get_qdrant_service()
        
        # Test 1: Get all textbook lessons
        print("1. Testing get all textbook lessons...")
        result = await qdrant_service.get_lessons_by_type(content_type="textbook")
        
        if result.get("success"):
            lessons = result.get("lessons", [])
            print(f"✅ Found {len(lessons)} textbook lessons")
            
            # Show first few
            for i, lesson in enumerate(lessons[:3], 1):
                print(f"   {i}. Book: {lesson.get('book_id')}, Lesson: {lesson.get('lesson_id')}")
                print(f"      File URL: {lesson.get('file_url', 'N/A')}")
        else:
            print(f"❌ Failed: {result.get('error')}")
        
        # Test 2: Get lessons by book_id
        print("\n2. Testing get lessons by book_id...")
        result = await qdrant_service.get_lessons_by_type(content_type="textbook", book_id="test_real")
        
        if result.get("success"):
            lessons = result.get("lessons", [])
            print(f"✅ Found {len(lessons)} lessons for book_id=test_real")
        else:
            print(f"❌ Failed: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

async def test_guides_api():
    """Test API get guides"""
    print("\n🔍 Testing Guides API")
    print("=" * 40)
    
    try:
        from app.services.qdrant_service import get_qdrant_service
        
        qdrant_service = get_qdrant_service()
        
        # Test get all guides
        print("Testing get all guides...")
        result = await qdrant_service.get_lessons_by_type(content_type="guide")
        
        if result.get("success"):
            guides = result.get("lessons", [])
            print(f"✅ Found {len(guides)} guides")
            
            # Show guides
            for i, guide in enumerate(guides, 1):
                print(f"   {i}. Book: {guide.get('book_id')}, Lesson: {guide.get('lesson_id')}")
                print(f"      File URL: {guide.get('file_url', 'N/A')}")
        else:
            print(f"❌ Failed: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

async def test_delete_functionality():
    """Test delete functionality"""
    print("\n🔍 Testing Delete Functionality")
    print("=" * 40)
    
    try:
        from app.services.qdrant_service import get_qdrant_service
        
        qdrant_service = get_qdrant_service()
        
        # Test get file URLs for deletion
        print("Testing get file URLs for deletion...")
        file_urls = await qdrant_service.get_file_urls_for_deletion("test_real", "lesson_real")
        
        print(f"✅ Found {len(file_urls)} file URLs for deletion:")
        for url in file_urls:
            print(f"   - {url}")
            
        # Test Supabase delete by URL
        if file_urls:
            print("\nTesting Supabase delete by URL...")
            from app.services.supabase_storage_service import get_supabase_storage_service
            
            supabase_service = get_supabase_storage_service()
            if supabase_service.is_available():
                # Test URL parsing (don't actually delete)
                test_url = file_urls[0]
                print(f"Testing URL parsing for: {test_url}")
                
                # Extract file path from URL for testing
                if "/storage/v1/object/public/" in test_url:
                    url_parts = test_url.split("/storage/v1/object/public/")
                    if len(url_parts) > 1:
                        path_with_bucket = url_parts[1].split('?')[0]
                        path_parts = path_with_bucket.split('/', 1)
                        if len(path_parts) > 1:
                            file_path = path_parts[1]
                            print(f"✅ Extracted file path: {file_path}")
                        else:
                            print("❌ Could not extract file path")
                    else:
                        print("❌ Invalid URL format")
                else:
                    print("❌ Not a Supabase storage URL")
            else:
                print("⚠️ Supabase service not available")
                
    except Exception as e:
        print(f"❌ Error: {e}")

async def main():
    """Main test function"""
    print("🚀 Testing Updated APIs")
    print("=" * 50)
    
    # Test Supabase DOCX support
    docx_url = await test_supabase_docx_support()
    
    # Test lessons API
    await test_lessons_api()
    
    # Test guides API
    await test_guides_api()
    
    # Test delete functionality
    await test_delete_functionality()
    
    print("\n✅ All tests completed!")
    print("\n📖 API Usage Examples:")
    print("   GET /api/v1/pdf/lessons                    # Get all textbook lessons")
    print("   GET /api/v1/pdf/lessons?book_id=hoa12      # Get lessons for specific book")
    print("   GET /api/v1/pdf/guides                     # Get all guides")
    print("   GET /api/v1/pdf/guides?book_id=guide_hoa12 # Get guides for specific book")
    print("   DELETE /api/v1/pdf?book_id=hoa12           # Delete entire book + files")
    print("   DELETE /api/v1/pdf?book_id=hoa12&lesson_id=bai1  # Delete specific lesson + file")

if __name__ == "__main__":
    asyncio.run(main())
