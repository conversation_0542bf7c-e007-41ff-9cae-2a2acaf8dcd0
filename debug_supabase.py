#!/usr/bin/env python3
"""
Debug script để kiểm tra Supabase integration
"""

import asyncio
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def debug_supabase():
    """Debug Supabase configuration và connection"""
    print("🔍 Debugging Supabase Integration")
    print("=" * 50)
    
    # 1. Check environment variables
    print("\n1. Environment Variables:")
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY") 
    bucket_name = os.getenv("SUPABASE_BUCKET_NAME", "pdf-documents")
    
    print(f"   SUPABASE_URL: {supabase_url or 'NOT SET'}")
    print(f"   SUPABASE_KEY: {'***' + supabase_key[-10:] if supabase_key else 'NOT SET'}")
    print(f"   SUPABASE_BUCKET_NAME: {bucket_name}")
    
    if not supabase_url or not supabase_key:
        print("\n❌ Missing Supabase configuration!")
        print("   Add to .env file:")
        print("   SUPABASE_URL=https://your-project.supabase.co")
        print("   SUPABASE_KEY=your-supabase-anon-key")
        return
    
    # 2. Test Supabase client initialization
    print("\n2. Testing Supabase Client:")
    try:
        from supabase import create_client
        
        client = create_client(supabase_url, supabase_key)
        print("✅ Supabase client created successfully")
        
        # Test connection by listing buckets
        try:
            buckets = client.storage.list_buckets()
            print(f"✅ Connection successful! Found {len(buckets)} buckets")
            
            bucket_names = [bucket.name for bucket in buckets]
            print(f"   Available buckets: {bucket_names}")
            
            if bucket_name not in bucket_names:
                print(f"⚠️ Bucket '{bucket_name}' not found!")
                print(f"   Create bucket '{bucket_name}' in Supabase Dashboard")
            else:
                print(f"✅ Bucket '{bucket_name}' exists")
                
        except Exception as e:
            print(f"❌ Connection test failed: {e}")
            
    except ImportError:
        print("❌ Supabase library not installed")
        print("   Run: pip install supabase>=2.0.0")
        return
    except Exception as e:
        print(f"❌ Client creation failed: {e}")
        return
    
    # 3. Test service initialization
    print("\n3. Testing Service:")
    try:
        from app.services.supabase_storage_service import get_supabase_storage_service
        
        service = get_supabase_storage_service()
        is_available = service.is_available()
        
        if is_available:
            print("✅ Supabase storage service is available")
        else:
            print("❌ Supabase storage service is not available")
            
    except Exception as e:
        print(f"❌ Service test failed: {e}")
    
    # 4. Test upload functionality
    print("\n4. Testing Upload:")
    try:
        from app.services.supabase_storage_service import get_supabase_storage_service
        
        service = get_supabase_storage_service()
        if not service.is_available():
            print("⚠️ Service not available, skipping upload test")
            return
        
        # Create minimal PDF content
        test_pdf = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj
4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test PDF) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF"""
        
        print("📤 Uploading test PDF...")
        result = await service.upload_pdf_file(
            file_content=test_pdf,
            book_id="debug_test",
            lesson_id="test_lesson",
            original_filename="debug_test.pdf"
        )
        
        if result.get("success"):
            print("✅ Upload successful!")
            print(f"   File URL: {result.get('file_url')}")
            print(f"   Upload time: {result.get('uploaded_at')}")
            print(f"   File path: {result.get('file_path')}")
            
            # Test if URL is accessible
            import httpx
            async with httpx.AsyncClient() as http_client:
                try:
                    response = await http_client.head(result.get('file_url'))
                    if response.status_code == 200:
                        print("✅ File URL is accessible")
                    else:
                        print(f"⚠️ File URL returned status: {response.status_code}")
                except Exception as e:
                    print(f"⚠️ Could not test URL accessibility: {e}")
                    
        else:
            print(f"❌ Upload failed: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Upload test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_supabase())
