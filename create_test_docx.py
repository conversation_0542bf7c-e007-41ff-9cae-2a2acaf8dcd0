#!/usr/bin/env python3
"""
Script để tạo file DOCX test
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_test_docx():
    """Tạo file DOCX test"""
    try:
        from docx import Document
        
        # Create a new document
        doc = Document()
        
        # Add title
        title = doc.add_heading('Hướng dẫn sử dụng sách giáo khoa Hóa học 12', 0)
        
        # Add content
        doc.add_heading('1. Chuẩn bị trước khi học', level=1)
        doc.add_paragraph('Trước khi bắt đầu học bài mới, học sinh cần:')
        doc.add_paragraph('• Đọc trước nội dung bài học', style='List Bullet')
        doc.add_paragraph('• Chuẩn bị dụng cụ thí nghiệm cần thiết', style='List Bullet')
        doc.add_paragraph('• Ôn tập kiến thức liên quan', style='List Bullet')
        
        doc.add_heading('2. Trong quá trình học', level=1)
        doc.add_paragraph('Trong giờ học, học sinh nên:')
        doc.add_paragraph('• Ghi chú những điểm quan trọng', style='List Bullet')
        doc.add_paragraph('• Tham gia thảo luận tích cực', style='List Bullet')
        doc.add_paragraph('• Thực hành các thí nghiệm cẩn thận', style='List Bullet')
        
        doc.add_heading('3. Sau khi học', level=1)
        doc.add_paragraph('Sau giờ học, học sinh cần:')
        doc.add_paragraph('• Ôn tập và làm bài tập', style='List Bullet')
        doc.add_paragraph('• Tổng kết kiến thức đã học', style='List Bullet')
        doc.add_paragraph('• Chuẩn bị cho bài học tiếp theo', style='List Bullet')
        
        doc.add_heading('4. Lưu ý quan trọng', level=1)
        doc.add_paragraph('Một số lưu ý khi học Hóa học:')
        doc.add_paragraph('• An toàn trong phòng thí nghiệm', style='List Bullet')
        doc.add_paragraph('• Ghi chép đầy đủ các phản ứng hóa học', style='List Bullet')
        doc.add_paragraph('• Thực hành thường xuyên để nhớ lâu', style='List Bullet')
        
        # Save file
        filename = 'test_guide_hoa12.docx'
        doc.save(filename)
        
        print(f"✅ Created DOCX file: {filename}")
        return filename
        
    except ImportError:
        print("❌ python-docx not installed. Run: pip install python-docx")
        return create_minimal_docx()

def create_minimal_docx():
    """Tạo file DOCX minimal"""
    import zipfile
    
    filename = 'test_guide_hoa12_minimal.docx'
    
    with zipfile.ZipFile(filename, 'w', zipfile.ZIP_DEFLATED) as docx_zip:
        # Add [Content_Types].xml
        content_types = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
    <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
    <Default Extension="xml" ContentType="application/xml"/>
    <Override PartName="/word/document.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml"/>
</Types>'''
        docx_zip.writestr('[Content_Types].xml', content_types)
        
        # Add _rels/.rels
        rels = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="word/document.xml"/>
</Relationships>'''
        docx_zip.writestr('_rels/.rels', rels)
        
        # Add word/document.xml
        document = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
    <w:body>
        <w:p>
            <w:r>
                <w:t>Hướng dẫn sử dụng sách giáo khoa Hóa học 12</w:t>
            </w:r>
        </w:p>
        <w:p>
            <w:r>
                <w:t>1. Chuẩn bị trước khi học</w:t>
            </w:r>
        </w:p>
        <w:p>
            <w:r>
                <w:t>- Đọc trước bài học</w:t>
            </w:r>
        </w:p>
        <w:p>
            <w:r>
                <w:t>- Chuẩn bị dụng cụ thí nghiệm</w:t>
            </w:r>
        </w:p>
        <w:p>
            <w:r>
                <w:t>2. Trong quá trình học</w:t>
            </w:r>
        </w:p>
        <w:p>
            <w:r>
                <w:t>- Ghi chú những điểm quan trọng</w:t>
            </w:r>
        </w:p>
        <w:p>
            <w:r>
                <w:t>- Thực hành các thí nghiệm</w:t>
            </w:r>
        </w:p>
        <w:p>
            <w:r>
                <w:t>3. Sau khi học</w:t>
            </w:r>
        </w:p>
        <w:p>
            <w:r>
                <w:t>- Ôn tập và làm bài tập</w:t>
            </w:r>
        </w:p>
        <w:p>
            <w:r>
                <w:t>- Tổng kết kiến thức</w:t>
            </w:r>
        </w:p>
    </w:body>
</w:document>'''
        docx_zip.writestr('word/document.xml', document)
    
    print(f"✅ Created minimal DOCX file: {filename}")
    return filename

if __name__ == "__main__":
    filename = create_test_docx()
    
    print(f"\n📋 File created: {filename}")
    print(f"📁 File location: {Path(filename).absolute()}")
    
    print(f"\n🚀 Test with API:")
    print(f"curl -X POST 'http://localhost:8000/api/v1/pdf/import' \\")
    print(f"     -F 'file=@{filename}' \\")
    print(f"     -F 'book_id=guide_hoa12' \\")
    print(f"     -F 'lesson_id=huong_dan_su_dung' \\")
    print(f"     -F 'create_embeddings=true' \\")
    print(f"     -F 'isImportGuide=true'")
    
    print(f"\n📖 Or use PowerShell:")
    print(f"$response = Invoke-RestMethod -Uri 'http://localhost:8000/api/v1/pdf/import' -Method POST -Form @{{")
    print(f"    'file' = Get-Item '{filename}'")
    print(f"    'book_id' = 'guide_hoa12'")
    print(f"    'lesson_id' = 'huong_dan_su_dung'")
    print(f"    'create_embeddings' = 'true'")
    print(f"    'isImportGuide' = 'true'")
    print(f"}}")
    print(f"$response | ConvertTo-Json -Depth 10")
