# Supabase Integration - PDF Storage Service

## Tổng quan

Dự án đã được tích hợp với Supabase Storage để lưu trữ file PDF và cung cấp URL truy cập. Khi import PDF qua API `/api/v1/pdf/import`, file sẽ được:

1. Upload lên Supabase Storage với cấu trúc thư mục theo `bookId`
2. Lưu `fileUrl` vào Qdrant metadata để truy xuất sau này
3. Trả về thông tin file URL trong response

## Cấu hình

### 1. Cài đặt dependencies

```bash
pip install supabase>=2.0.0
```

### 2. Cấu hình environment variables

Thêm vào file `.env`:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-supabase-anon-key
SUPABASE_BUCKET_NAME=pdf-documents
```

### 3. Tạo bucket trên Supabase

1. <PERSON><PERSON><PERSON> nhập vào Supabase Dashboard
2. Vào Storage > Create bucket
3. Tạo bucket với tên `pdf-documents` (hoặc tên khác theo cấu hình)
4. Cấu hình public access nếu cần

## API Endpoints

### 1. Import PDF với Supabase Storage

**Endpoint:** `POST /api/v1/pdf/import`

**Mô tả:** Import PDF và upload lên Supabase Storage

**Request:**
```bash
curl -X POST "http://localhost:8000/api/v1/pdf/import" \
     -F "file=@textbook.pdf" \
     -F "book_id=hoa12" \
     -F "lesson_id=bai1" \
     -F "create_embeddings=true"
```

**Response:**
```json
{
    "success": true,
    "task_id": "abc123",
    "book_id": "hoa12",
    "filename": "textbook.pdf",
    "lesson_id": "bai1",
    "status": "processing",
    "import_type": "textbook",
    "message": "Quick textbook analysis task created successfully.",
    "endpoints": {
        "check_status": "/api/v1/tasks/abc123/status",
        "get_result": "/api/v1/tasks/abc123/result"
    }
}
```

**Kết quả sau khi xử lý xong:**
```json
{
    "success": true,
    "book_id": "hoa12",
    "filename": "textbook.pdf",
    "lesson_id": "bai1",
    "file_url": "https://your-project.supabase.co/storage/v1/object/public/pdf-documents/hoa12/hoa12_bai1_20241216_143022_abc12345.pdf",
    "book_structure": {...},
    "embeddings_created": true,
    "message": "PDF processing completed successfully"
}
```

### 2. Lấy tất cả bài học

**Endpoint:** `GET /api/v1/pdf/lessons`

**Mô tả:** Lấy danh sách tất cả bài học đã import với thông tin file URL

**Request:**
```bash
curl -X GET "http://localhost:8000/api/v1/pdf/lessons"
```

**Response:**
```json
{
    "success": true,
    "data": {
        "lessons": [
            {
                "book_id": "hoa12",
                "lesson_id": "bai1",
                "file_url": "https://your-project.supabase.co/storage/v1/object/public/pdf-documents/hoa12/hoa12_bai1_20241216_143022_abc12345.pdf",
                "processed_at": "2024-12-16T14:30:22.123456",
                "content_type": "textbook",
                "collection_name": "textbook_hoa12",
                "total_chunks": 45
            },
            {
                "book_id": "toan10",
                "lesson_id": "bai2",
                "file_url": "https://your-project.supabase.co/storage/v1/object/public/pdf-documents/toan10/toan10_bai2_20241216_140015_def67890.pdf",
                "processed_at": "2024-12-16T14:00:15.654321",
                "content_type": "textbook",
                "collection_name": "textbook_toan10",
                "total_chunks": 32
            }
        ],
        "total_lessons": 2,
        "collections_processed": 2
    },
    "message": "Retrieved 2 lessons successfully"
}
```

## Cấu trúc lưu trữ

### Supabase Storage Structure

```
pdf-documents/
├── hoa12/
│   ├── hoa12_bai1_20241216_143022_abc12345.pdf
│   ├── hoa12_bai2_20241216_143500_def67890.pdf
│   └── hoa12_20241216_144000_ghi12345.pdf (không có lesson_id)
├── toan10/
│   ├── toan10_bai1_20241216_140015_jkl67890.pdf
│   └── toan10_bai2_20241216_141000_mno12345.pdf
└── vatly11/
    └── vatly11_20241216_142000_pqr67890.pdf
```

### Quy tắc đặt tên file

- **Có lesson_id:** `{book_id}_{lesson_id}_{timestamp}_{unique_id}.pdf`
- **Không có lesson_id:** `{book_id}_{timestamp}_{unique_id}.pdf`

### Qdrant Metadata Structure

Metadata được lưu trong Qdrant với các field:

```json
{
    "book_id": "hoa12",
    "lesson_id": "bai1",
    "type": "metadata",
    "content_type": "textbook",
    "file_url": "https://your-project.supabase.co/storage/v1/object/public/pdf-documents/hoa12/hoa12_bai1_20241216_143022_abc12345.pdf",
    "total_chunks": 45,
    "processed_at": "2024-12-16T14:30:22.123456",
    "model": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
    "chunk_size": 1500,
    "chunk_overlap": 100
}
```

## Lưu ý

1. **Backup:** File PDF được lưu trữ an toàn trên Supabase Storage
2. **Access Control:** Cấu hình quyền truy cập bucket theo nhu cầu
3. **Storage Limit:** Kiểm tra giới hạn storage của Supabase plan
4. **URL Expiry:** Public URL không hết hạn, private URL có thể cần refresh
5. **Error Handling:** Nếu Supabase không khả dụng, hệ thống vẫn hoạt động bình thường nhưng không có file_url

## Troubleshooting

### Lỗi "Supabase service not available"

1. Kiểm tra `SUPABASE_URL` và `SUPABASE_KEY` trong `.env`
2. Đảm bảo bucket `pdf-documents` đã được tạo
3. Kiểm tra quyền truy cập bucket
4. Cài đặt thư viện: `pip install supabase>=2.0.0`

### File upload thất bại

1. Kiểm tra kích thước file (giới hạn Supabase)
2. Kiểm tra định dạng file (chỉ hỗ trợ PDF)
3. Kiểm tra quyền write vào bucket
4. Xem log chi tiết trong console

### Không thấy file_url trong response

1. Kiểm tra Supabase service có khả dụng không
2. Xem log để biết lỗi upload cụ thể
3. File vẫn được xử lý bình thường, chỉ thiếu URL
