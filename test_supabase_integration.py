#!/usr/bin/env python3
"""
Script test cho Supabase integration
"""

import asyncio
import httpx
import json
from pathlib import Path

BASE_URL = "http://localhost:8000/api/v1"

async def test_get_all_lessons():
    """Test API lấy tất cả bài học"""
    print("🔍 Testing GET /pdf/lessons...")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{BASE_URL}/pdf/lessons")
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ API call successful!")
                print(f"Total lessons: {data.get('data', {}).get('total_lessons', 0)}")
                
                lessons = data.get('data', {}).get('lessons', [])
                if lessons:
                    print("\n📚 Sample lessons:")
                    for i, lesson in enumerate(lessons[:3]):  # Show first 3 lessons
                        print(f"  {i+1}. Book: {lesson.get('book_id')}, Lesson: {lesson.get('lesson_id')}")
                        print(f"     File URL: {lesson.get('file_url', 'N/A')}")
                        print(f"     Processed: {lesson.get('processed_at', 'N/A')}")
                        print()
                else:
                    print("📝 No lessons found in database")
                    
            else:
                print(f"❌ API call failed: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

async def test_pdf_import_status():
    """Test kiểm tra status của các task import"""
    print("\n🔍 Testing task status...")
    
    # Đây chỉ là ví dụ - trong thực tế bạn cần task_id thực
    # Bạn có thể lấy task_id từ response của /pdf/import
    
    print("💡 To test PDF import:")
    print("1. Upload a PDF file using:")
    print(f'   curl -X POST "{BASE_URL}/pdf/import" \\')
    print('        -F "file=@your_file.pdf" \\')
    print('        -F "book_id=test_book" \\')
    print('        -F "lesson_id=test_lesson" \\')
    print('        -F "create_embeddings=true"')
    print("\n2. Use the returned task_id to check status:")
    print(f'   curl -X GET "{BASE_URL}/tasks/{{task_id}}/status"')
    print("\n3. Get final result:")
    print(f'   curl -X GET "{BASE_URL}/tasks/{{task_id}}/result"')

def test_supabase_service():
    """Test Supabase service initialization"""
    print("\n🔍 Testing Supabase service...")
    
    try:
        from app.services.supabase_storage_service import get_supabase_storage_service
        
        service = get_supabase_storage_service()
        is_available = service.is_available()
        
        if is_available:
            print("✅ Supabase service is available and configured")
        else:
            print("⚠️ Supabase service is not available")
            print("   Check your SUPABASE_URL and SUPABASE_KEY in .env file")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Run: pip install supabase>=2.0.0")
    except Exception as e:
        print(f"❌ Error: {e}")

def show_configuration_info():
    """Hiển thị thông tin cấu hình"""
    print("\n📋 Configuration Info:")
    print("=" * 50)
    
    try:
        from app.core.config import settings
        
        print(f"Supabase URL: {settings.SUPABASE_URL or 'Not configured'}")
        print(f"Supabase Key: {'***' if settings.SUPABASE_KEY else 'Not configured'}")
        print(f"Bucket Name: {settings.SUPABASE_BUCKET_NAME}")
        
        if not settings.SUPABASE_URL or not settings.SUPABASE_KEY:
            print("\n⚠️ Supabase not configured. Add to .env:")
            print("SUPABASE_URL=https://your-project.supabase.co")
            print("SUPABASE_KEY=your-supabase-anon-key")
            print("SUPABASE_BUCKET_NAME=pdf-documents")
            
    except Exception as e:
        print(f"❌ Error loading config: {e}")

async def main():
    """Main test function"""
    print("🚀 Supabase Integration Test")
    print("=" * 50)
    
    # Test configuration
    show_configuration_info()
    
    # Test Supabase service
    test_supabase_service()
    
    # Test API endpoints
    await test_get_all_lessons()
    
    # Show import instructions
    await test_pdf_import_status()
    
    print("\n✅ Test completed!")
    print("\n📖 For more information, see SUPABASE_INTEGRATION.md")

if __name__ == "__main__":
    asyncio.run(main())
