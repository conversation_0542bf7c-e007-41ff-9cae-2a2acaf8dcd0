#!/usr/bin/env python3
"""
Simple test để tạo file DOCX thật và test upload
"""

import asyncio
import sys
from pathlib import Path
import tempfile
import os

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

def create_real_docx_file():
    """Tạo file DOCX thật với python-docx"""
    try:
        from docx import Document
        
        # Create a new document
        doc = Document()
        
        # Add title
        title = doc.add_heading('Hướng dẫn sử dụng sách giáo khoa Hóa học 12', 0)
        
        # Add content
        doc.add_heading('1. Chuẩn bị trước khi học', level=1)
        doc.add_paragraph('Trước khi bắt đầu học bài mới, học sinh cần:')
        doc.add_paragraph('• Đ<PERSON><PERSON> trước nội dung bài học', style='List Bullet')
        doc.add_paragraph('• <PERSON><PERSON><PERSON> bị dụng cụ thí nghiệm cần thiết', style='List Bullet')
        doc.add_paragraph('• Ôn tập kiến thức liên quan', style='List Bullet')
        
        doc.add_heading('2. Trong quá trình học', level=1)
        doc.add_paragraph('Trong giờ học, học sinh nên:')
        doc.add_paragraph('• Ghi chú những điểm quan trọng', style='List Bullet')
        doc.add_paragraph('• Tham gia thảo luận tích cực', style='List Bullet')
        doc.add_paragraph('• Thực hành các thí nghiệm cẩn thận', style='List Bullet')
        
        doc.add_heading('3. Sau khi học', level=1)
        doc.add_paragraph('Sau giờ học, học sinh cần:')
        doc.add_paragraph('• Ôn tập và làm bài tập', style='List Bullet')
        doc.add_paragraph('• Tổng kết kiến thức đã học', style='List Bullet')
        doc.add_paragraph('• Chuẩn bị cho bài học tiếp theo', style='List Bullet')
        
        # Save to temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix='.docx', delete=False)
        doc.save(temp_file.name)
        
        return temp_file.name
        
    except ImportError:
        print("⚠️ python-docx not installed. Creating minimal DOCX...")
        return create_minimal_docx_file()

def create_minimal_docx_file():
    """Tạo file DOCX minimal nếu không có python-docx"""
    import zipfile
    import tempfile
    
    temp_file = tempfile.NamedTemporaryFile(suffix='.docx', delete=False)
    
    with zipfile.ZipFile(temp_file.name, 'w', zipfile.ZIP_DEFLATED) as docx_zip:
        # Add [Content_Types].xml
        content_types = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
    <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
    <Default Extension="xml" ContentType="application/xml"/>
    <Override PartName="/word/document.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml"/>
</Types>'''
        docx_zip.writestr('[Content_Types].xml', content_types)
        
        # Add _rels/.rels
        rels = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="word/document.xml"/>
</Relationships>'''
        docx_zip.writestr('_rels/.rels', rels)
        
        # Add word/document.xml
        document = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
    <w:body>
        <w:p>
            <w:r>
                <w:t>Hướng dẫn sử dụng sách giáo khoa Hóa học 12</w:t>
            </w:r>
        </w:p>
        <w:p>
            <w:r>
                <w:t>Đây là file DOCX test để kiểm tra upload functionality.</w:t>
            </w:r>
        </w:p>
    </w:body>
</w:document>'''
        docx_zip.writestr('word/document.xml', document)
    
    return temp_file.name

async def test_docx_upload_and_processing():
    """Test upload và processing DOCX"""
    print("🔍 Testing DOCX Upload and Processing")
    print("=" * 50)
    
    docx_file_path = None
    
    try:
        # Create DOCX file
        print("1. Creating DOCX file...")
        docx_file_path = create_real_docx_file()
        
        file_size = os.path.getsize(docx_file_path)
        print(f"✅ Created DOCX file: {docx_file_path}")
        print(f"   File size: {file_size} bytes")
        
        # Read file content
        with open(docx_file_path, 'rb') as f:
            file_content = f.read()
        
        # Test Supabase upload
        print("\n2. Testing Supabase upload...")
        from app.services.supabase_storage_service import get_supabase_storage_service
        
        supabase_service = get_supabase_storage_service()
        if not supabase_service.is_available():
            print("❌ Supabase service not available")
            return
        
        upload_result = await supabase_service.upload_docx_file(
            file_content=file_content,
            book_id="test_guide_real",
            lesson_id="huong_dan_real",
            original_filename="huong_dan_hoa12.docx"
        )
        
        if upload_result.get("success"):
            print("✅ DOCX upload successful!")
            print(f"   File URL: {upload_result.get('file_url')}")
            print(f"   Upload time: {upload_result.get('uploaded_at')}")
            
            file_url = upload_result.get('file_url')
            uploaded_at = upload_result.get('uploaded_at')
            
            # Test Qdrant processing
            print("\n3. Testing Qdrant processing...")
            from app.services.qdrant_service import get_qdrant_service
            
            qdrant_service = get_qdrant_service()
            
            # Process as guide
            result = await qdrant_service.process_textbook(
                book_id="test_guide_real",
                content="Hướng dẫn sử dụng sách giáo khoa Hóa học 12. Đây là nội dung hướng dẫn chi tiết cho học sinh.",
                lesson_id="huong_dan_real",
                content_type="guide",
                file_url=file_url,
                uploaded_at=uploaded_at
            )
            
            if result.get("success"):
                print("✅ Guide processing successful!")
                print(f"   Collection: {result.get('collection_name')}")
                print(f"   Total chunks: {result.get('total_chunks')}")
                
                # Test get guides API
                print("\n4. Testing get guides API...")
                guides_result = await qdrant_service.get_lessons_by_type(
                    content_type="guide", 
                    book_id="test_guide_real"
                )
                
                if guides_result.get("success"):
                    guides = guides_result.get("lessons", [])
                    print(f"✅ Found {len(guides)} guides for test_guide_real")
                    
                    for guide in guides:
                        print(f"   - Book: {guide.get('book_id')}, Lesson: {guide.get('lesson_id')}")
                        print(f"     File URL: {guide.get('file_url')}")
                        print(f"     Upload time: {guide.get('uploaded_at')}")
                else:
                    print(f"❌ Get guides failed: {guides_result.get('error')}")
            else:
                print(f"❌ Guide processing failed: {result.get('error')}")
        else:
            print(f"❌ DOCX upload failed: {upload_result.get('error')}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        if docx_file_path and os.path.exists(docx_file_path):
            os.unlink(docx_file_path)
            print(f"\n🧹 Cleaned up temporary file: {docx_file_path}")

async def main():
    """Main test function"""
    await test_docx_upload_and_processing()
    
    print("\n✅ DOCX test completed!")
    print("\n📋 Next steps to test with real API:")
    print("   1. Start FastAPI server: uvicorn app.main:app --reload")
    print("   2. Start Celery worker: celery -A app.celery_app worker --loglevel=info")
    print("   3. Create a real DOCX file")
    print("   4. Use curl or Postman to test:")
    print("      curl -X POST 'http://localhost:8000/api/v1/pdf/import' \\")
    print("           -F 'file=@your_guide.docx' \\")
    print("           -F 'book_id=guide_hoa12' \\")
    print("           -F 'lesson_id=huong_dan' \\")
    print("           -F 'create_embeddings=true' \\")
    print("           -F 'isImportGuide=true'")

if __name__ == "__main__":
    asyncio.run(main())
